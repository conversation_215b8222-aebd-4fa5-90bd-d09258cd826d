version: 1
apps:
  - appID: alphafi-agent
    appDirPath: ../
    appPort: 8000
    command: ["uv", "run", "src/alphafi_agent/app.py"]

    # Dapr sidecar配置
    daprHTTPPort: 3550
    daprGRPCPort: 35500
    daprHTTPMaxRequestSize: 4
    daprHTTPReadBufferSize: 4
    logLevel: info
    appLogDestination: file
    daprdLogDestination: file

    # 健康检查配置
    appHealthCheckPath: "/health"
    appHealthProbeInterval: 5
    appHealthProbeTimeout: 3
    appHealthThreshold: 3          