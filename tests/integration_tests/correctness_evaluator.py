from openevals.llm import create_llm_as_judge
from openevals.prompts import CORRECTNESS_PROMPT

from alphafi_agent.llms import get_llm

correctness_evaluator = create_llm_as_judge(
    # CONCISENESS_PROMPT is just an f-string
    prompt=CORRECTNESS_PROMPT,
    feedback_key="correctness",
    judge=get_llm(),
)

inputs = "How much has the price of doodads changed in the past year?"
reference_outputs = "The price of doodads has decreased by 50% in the past year."
outputs = "Doodads have increased in price by 10% in the past year."

eval_result = correctness_evaluator(
  inputs=inputs,
  outputs=outputs,
  reference_outputs=reference_outputs
)

print(eval_result)
