import asyncio
import csv
import uuid
from pathlib import Path

from langgraph.constants import CONF
from openevals.llm import create_llm_as_judge
from openevals.prompts import CORRECTNESS_PROMPT

from alphafi_agent.constants import CONFIG_KEY_THREAD_ID
from alphafi_agent.llms import get_llm
from alphafi_agent.graph import builder

# 创建评估器
correctness_evaluator = create_llm_as_judge(
    prompt=CORRECTNESS_PROMPT,
    feedback_key="correctness",
    continuous=True,
    choices=[0.0, 0.1,0.2,0.3,0.4,0.5,0.6,0.7,0.8,0.9, 1.0],
    judge=get_llm(),
)

# 编译图
graph = builder.compile()

async def evaluate_dataset(csv_path: str):
    """评估CSV文件中的所有输入输出对

    Args:
        csv_path: CSV文件路径，包含inputs和reference_outputs列
    """
    # 读取CSV文件
    data = []
    with open(csv_path, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            data.append({
                'inputs': row['inputs'],
                'reference_outputs': row['reference_outputs']
            })

    # 评估结果列表
    results = []

    # 循环评估每个输入
    for idx, item in enumerate(data, 1):
        print(f"评估第 {idx}/{len(data)} 个输入...")
        inputs = item['inputs']
        reference_outputs = item['reference_outputs']

        # 配置
        config = {
            CONF: {},
            "user_id": "tester",
        }

        # 调用图
        result = await graph.ainvoke(
            {"messages": [{"role": "user", "content": inputs}]},
            {**config, CONFIG_KEY_THREAD_ID: str(uuid.uuid4())},
        )

        # 获取输出
        outputs = result["messages"][-1].content
        print(f"输入: {inputs}")
        print(f"模型输出: {outputs}")
        print(f"参考输出: {reference_outputs}")

        # 评估
        eval_result = correctness_evaluator(
            inputs=inputs,
            outputs=outputs,
            reference_outputs=reference_outputs
        )
        print(f"评估结果: {eval_result}")
        print("-" * 50)

        # 保存结果
        results.append({
            'inputs': inputs,
            'outputs': outputs,
            'reference_outputs': reference_outputs,
            'eval_result': eval_result
        })

    return results

async def main():
    # CSV文件路径
    csv_path = Path(__file__).parent / "dataset_eval.csv"
    # 评估数据集
    evaluate_results = await evaluate_dataset(str(csv_path))
    #统计evaluate_results.score属性为True的数据条数
    print(f"正确率：{sum([item['eval_result']['score'] for item in evaluate_results]) / len(evaluate_results) * 100:.2f}%")


if __name__ == "__main__":
    asyncio.run(main())