from openevals.llm import create_llm_as_judge
from openevals.prompts import HALLUCINATION_PROMPT

from alphafi_agent.llms import get_llm

inputs = "What is a doodad?"
outputs = "I know the answer. A doodad is a kitten."
context = "A doodad is a self-replicating swarm of nanobots. They are extremely dangerous and should be avoided at all costs. Some safety precautions when working with them include wearing gloves and a mask."

llm_as_judge = create_llm_as_judge(
    prompt=HALLUCINATION_PROMPT,
    feedback_key="hallucination",
    judge=get_llm(),
)

eval_result = llm_as_judge(
    inputs=inputs,
    outputs=outputs,
    context=context,
    reference_outputs="",
)

print(eval_result)
