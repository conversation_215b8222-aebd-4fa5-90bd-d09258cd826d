from langchain_core.language_models import BaseChatModel
from langchain.chat_models import init_chat_model
from langchain_ollama import ChatOllama
from langchain_core.embeddings import Embeddings
from langchain_ollama import OllamaEmbeddings
from langchain_core.embeddings import DeterministicFakeEmbedding
from langchain_google_genai import GoogleGenerativeAIEmbeddings
import os
from dotenv import load_dotenv
load_dotenv()

def get_llm()-> BaseChatModel:
    if os.getenv("MODEL_PROVIDER") == "ollama":
        return ChatOllama(model=os.getenv('MODEL'), base_url=os.getenv('API_BASE'))
    return init_chat_model(os.getenv('MODEL'), model_provider=os.getenv("MODEL_PROVIDER"))

def get_embeddings()->Embeddings:
    if os.getenv("EMBEDDING_PROVIDER") == "fake":
        return DeterministicFakeEmbedding(size=4096)
    elif os.getenv("EMBEDDING_PROVIDER") == "gemini":
        return GoogleGenerativeAIEmbeddings(model=os.getenv('EMBEDDING_MODEL'))
    return OllamaEmbeddings(base_url=os.getenv('EMBEDDING_API_BASE'), model=os.getenv('EMBEDDING_MODEL'))
