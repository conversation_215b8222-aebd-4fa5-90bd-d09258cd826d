"""Graphs that extract memories on a schedule and handle different user intents."""
import asyncio
import logging
import os

from langchain_core.runnables import RunnableConfig
from langchain_mcp_adapters.client import MultiServerMCPClient
from langgraph.graph import END, StateGraph, START
from langgraph.prebuilt import ToolNode

import alphafi_agent.prompts
from alphafi_agent import configuration, utils, retriever
from alphafi_agent.llms import get_llm
from alphafi_agent.mcp_config import MCP_SERVER_CONFIG
from alphafi_agent.protocol.lido import stake_action
from alphafi_agent.protocol.recommendation import recommend_action, add_liquidity_action
from alphafi_agent.state import State

logger = logging.getLogger(__name__)

from dotenv import load_dotenv
load_dotenv()
llm = get_llm()


# 意图处理节点
INTENT_ON_CHAIN_OPERATION = "on_chain_operation"
INTENT_RECOMMENDATION_ACTION = "recommendation_action"
INTENT_TOKEN_MARKET_DATA = "token_market_data"
INTENT_YIELD_POOL_DATA = "yield_pool_data"
INTENT_DEFI_KNOWLEDGE_QUERY = "defi_knowledge_query"

# 工具节点名称变量
TOOL_ON_CHAIN_OPERATION = "tool_on_chain_operation"
TOOL_RECOMMENDATION_ACTION = "tool_recommendation_action"
TOOL_TOKEN_MARKET_DATA = "tool_token_market_data"
TOOL_YIELD_POOL_DATA = "tool_yield_pool_data"
TOOL_DEFI_KNOWLEDGE_QUERY = "tool_defi_knowledge_query"
NODE_RECOGNIZE_INTENT = "recognize_intent"
GRAPH_NAME = "AlphaFiAgent"

# 
OUT_PUT= "output"

async def get_on_chain_operation_tools():
    # 获取基础工具列表
    llm_tools = [stake_action]
    return llm_tools

async def get_recommend_tools():
    # 获取基础工具列表
    llm_tools = [add_liquidity_action]
    return llm_tools
async def get_token_market_tools():
    mcp_client = MultiServerMCPClient(MCP_SERVER_CONFIG)
    # 获取基础工具列表
    return [*(await mcp_client.get_tools())]

async def get_yield_pool_tools():
    mcp_client = MultiServerMCPClient(MCP_SERVER_CONFIG)
    # 获取基础工具列表
    return [*(await mcp_client.get_tools())]


async def get_defi_knowledge_tools():
    return [retriever.retriever_tool]

# Define intent recognition node
async def recognize_intent(state: State):
    filter_msgs = utils.filter_message(state.messages)
    # 强制禁止流式输出
    msg = await asyncio.get_event_loop().run_in_executor(
        None, lambda: llm.invoke(
            [{"role": "system", "content": alphafi_agent.prompts.INTENT_CLASSIFICATION_PROMPT}, *filter_msgs])
    )
    # msg = await llm.ainvoke(
    #     [{"role": "system", "content": alphafi_agent.prompts.INTENT_CLASSIFICATION_PROMPT}, *filter_msgs]
    # )
    intent = INTENT_DEFI_KNOWLEDGE_QUERY
    if INTENT_ON_CHAIN_OPERATION in msg.content:
        intent = INTENT_ON_CHAIN_OPERATION
    elif INTENT_RECOMMENDATION_ACTION in msg.content:
        intent = INTENT_RECOMMENDATION_ACTION
    elif INTENT_TOKEN_MARKET_DATA in msg.content:
        intent = INTENT_TOKEN_MARKET_DATA
    elif INTENT_YIELD_POOL_DATA in msg.content:
        intent = INTENT_YIELD_POOL_DATA
    elif "AlphaFi" in msg.content:
        # 自我介绍，直接返回
        return {"messages": [msg]}

    return {"intent": intent, "messages": state.messages, "test":"test123"}

# Define intent execution nodes
async def handle_on_chain_operation(state: State):
    filter_msgs = utils.filter_message(state.messages)
    msg = await llm.bind_tools(await get_on_chain_operation_tools()).ainvoke(
        [{"role": "system", "content": alphafi_agent.prompts.ON_CHAIN_OPERATION_PROMPT}, *filter_msgs]
    )
    return {"messages": [msg]}

async def handle_recommendation_action(state: State):
    filter_msgs = utils.filter_message(state.messages)
    msg = await llm.bind_tools(await get_recommend_tools(), tool_choice="any").ainvoke(
        [{"role": "system", "content": alphafi_agent.prompts.RECOMMENDATION_PROMPT}, *filter_msgs]
    )
    return {"messages": [msg]}

async def handle_token_market_data(state: State):
    filter_msgs = utils.filter_message(state.messages)
    msg = await llm.bind_tools(await get_token_market_tools()).ainvoke(
        [{"role": "system", "content": alphafi_agent.prompts.TOKEN_MARKET_DATA_PROMPT}, *filter_msgs]
    )
    return {"messages": [msg]}

async def handle_yield_pool_data(state: State):
    filter_msgs = utils.filter_message(state.messages)
    msg = await llm.bind_tools(await get_yield_pool_tools()).ainvoke(
        [{"role": "system", "content": alphafi_agent.prompts.YIELD_POOL_DATA_PROMPT}, *filter_msgs]
    )
    return {"messages": [msg]}

async def handle_defi_knowledge_query(state: State):
    filter_msgs = utils.filter_message(state.messages)
    msg = await llm.bind_tools(await get_defi_knowledge_tools()).ainvoke(
        [{"role": "system", "content": alphafi_agent.prompts.DEFI_KNOWLEDGE_QUERY_PROMPT}, *filter_msgs]
    )
    return {"messages": [msg]}

# 工具处理节点方法
async def tool_handle_on_chain_operation(state: State, config: RunnableConfig):
    bind_tools = await get_on_chain_operation_tools()
    return {"messages": await ToolNode(bind_tools).ainvoke(state.messages, config)}

async def tool_handle_recommendation_action(state: State, config: RunnableConfig):
    bind_tools = await get_recommend_tools()
    return {"messages": await ToolNode(bind_tools).ainvoke(state.messages, config)}

async def tool_handle_token_market_data(state: State, config: RunnableConfig):
    bind_tools = await get_token_market_tools()
    return {"messages": await ToolNode(bind_tools).ainvoke(state.messages, config)}

async def tool_handle_yield_pool_data(state: State, config: RunnableConfig):
    bind_tools = await get_yield_pool_tools()
    return {"messages": await ToolNode(bind_tools).ainvoke(state.messages, config)}

async def tool_handle_defi_knowledge_query(state: State, config: RunnableConfig):
    bind_tools = await get_defi_knowledge_tools()
    return {"messages": await ToolNode(bind_tools).ainvoke(state.messages, config)}

async def handle_output(state: State):
    filter_msgs = utils.filter_message(state.messages)
    msg = await llm.ainvoke(
        [{"role": "system", "content": alphafi_agent.prompts.OUTPUT_PROMPT}, *filter_msgs]
    )
    return {"messages": [msg]}

def intent_router(state: State):
    if state.intent:
        if INTENT_ON_CHAIN_OPERATION in state.intent:
            return INTENT_ON_CHAIN_OPERATION
        elif INTENT_RECOMMENDATION_ACTION in state.intent:
            return INTENT_RECOMMENDATION_ACTION
        elif INTENT_TOKEN_MARKET_DATA in state.intent:
            return INTENT_TOKEN_MARKET_DATA
        elif INTENT_YIELD_POOL_DATA in state.intent:
            return INTENT_YIELD_POOL_DATA
        else:
            return INTENT_DEFI_KNOWLEDGE_QUERY
    else:
        msg = state.messages[-1]
        if "AlphaFi" in msg.content:
            return END
    return INTENT_DEFI_KNOWLEDGE_QUERY

def handler_router(state: State):
    """Determine the next step based on the presence of tool calls."""
    msg = state.messages[-1]
    # Check if the message has tool_calls attribute and if it's not empty
    if hasattr(msg, 'tool_calls') and msg.tool_calls:
        if INTENT_ON_CHAIN_OPERATION in state.intent:
            return TOOL_ON_CHAIN_OPERATION
        elif INTENT_RECOMMENDATION_ACTION in state.intent:
            return TOOL_RECOMMENDATION_ACTION
        elif INTENT_TOKEN_MARKET_DATA in state.intent:
            return TOOL_TOKEN_MARKET_DATA
        elif INTENT_YIELD_POOL_DATA in state.intent:
            return TOOL_YIELD_POOL_DATA
        else:
            return TOOL_DEFI_KNOWLEDGE_QUERY
    # Otherwise, finish; user can send the next message
    return END
def tool_router(state: State):
    """Determine the next step based on the presence of tool calls."""
    msg = state.messages[-1]
    if hasattr(msg, 'name') and msg.name and msg.name.endswith("_action"):
        return END
    return OUT_PUT

# Create the graph + all nodes
builder = StateGraph(State, config_schema=configuration.Configuration)

# Add nodes
builder.add_node(NODE_RECOGNIZE_INTENT, recognize_intent)
builder.add_node(INTENT_ON_CHAIN_OPERATION, handle_on_chain_operation)
builder.add_node(INTENT_RECOMMENDATION_ACTION, handle_recommendation_action)
builder.add_node(INTENT_TOKEN_MARKET_DATA, handle_token_market_data)
builder.add_node(INTENT_YIELD_POOL_DATA, handle_yield_pool_data)
builder.add_node(INTENT_DEFI_KNOWLEDGE_QUERY, handle_defi_knowledge_query)

# 新增工具节点
builder.add_node(TOOL_ON_CHAIN_OPERATION, tool_handle_on_chain_operation)
builder.add_node(TOOL_RECOMMENDATION_ACTION, tool_handle_recommendation_action)
builder.add_node(TOOL_TOKEN_MARKET_DATA, tool_handle_token_market_data)
builder.add_node(TOOL_YIELD_POOL_DATA, tool_handle_yield_pool_data)
builder.add_node(TOOL_DEFI_KNOWLEDGE_QUERY, tool_handle_defi_knowledge_query)

builder.add_node(OUT_PUT,handle_output)

# Define edges
builder.add_edge(START, NODE_RECOGNIZE_INTENT)

builder.add_conditional_edges(
    NODE_RECOGNIZE_INTENT,
    intent_router,
    [INTENT_ON_CHAIN_OPERATION, INTENT_RECOMMENDATION_ACTION, INTENT_TOKEN_MARKET_DATA, INTENT_YIELD_POOL_DATA, INTENT_DEFI_KNOWLEDGE_QUERY, END]
)

# Add edges from intent nodes to tool nodes or end node
builder.add_conditional_edges(INTENT_ON_CHAIN_OPERATION, handler_router,[TOOL_ON_CHAIN_OPERATION, END])
builder.add_conditional_edges(INTENT_RECOMMENDATION_ACTION, handler_router,[TOOL_RECOMMENDATION_ACTION, END])
builder.add_conditional_edges(INTENT_TOKEN_MARKET_DATA, handler_router,[TOOL_TOKEN_MARKET_DATA, END])
builder.add_conditional_edges(INTENT_YIELD_POOL_DATA, handler_router,[TOOL_YIELD_POOL_DATA, END])
builder.add_conditional_edges(INTENT_DEFI_KNOWLEDGE_QUERY, handler_router,[TOOL_DEFI_KNOWLEDGE_QUERY, END])

# Add edges from tool nodes to OUT_PUT
builder.add_conditional_edges(TOOL_ON_CHAIN_OPERATION, tool_router,[OUT_PUT, END])
builder.add_conditional_edges(TOOL_RECOMMENDATION_ACTION, tool_router,[OUT_PUT, END])
builder.add_conditional_edges(TOOL_TOKEN_MARKET_DATA, tool_router,[OUT_PUT, END])
builder.add_conditional_edges(TOOL_YIELD_POOL_DATA, tool_router,[OUT_PUT, END])
builder.add_conditional_edges(TOOL_DEFI_KNOWLEDGE_QUERY, tool_router,[OUT_PUT, END])
builder.add_edge(OUT_PUT, END)

alphafi_graph = builder.compile()
alphafi_graph.name = GRAPH_NAME


__all__ = ["alphafi_graph", "builder"]
