"""Graphs that extract memories on a schedule."""

import asyncio
import logging

from langchain_core.messages import AIMessage
from langchain_core.runnables import RunnableConfig
from langchain_mcp_adapters.client import MultiServerMCPClient
from langgraph.graph import END, StateGraph
from langgraph.store.base import BaseStore

from alphafi_agent import configuration, tools, utils, retriever
from alphafi_agent.llms import get_llm
from alphafi_agent.mcp_config import MCP_SERVER_CONFIG
from alphafi_agent.protocol.lido import stake_action
from alphafi_agent.protocol.introduce_yourself import introduce_yourself
from alphafi_agent.protocol.recommendation import recommend_action
from alphafi_agent.protocol.token_price import market_data
from alphafi_agent.state import State
from langgraph.prebuilt import ToolNode
from langsmith import traceable

logger = logging.getLogger(__name__)

import os
from dotenv import load_dotenv
load_dotenv()
llm = get_llm()

#construct mcp tools

async def get_tools():
    mcp_client = MultiServerMCPClient(MCP_SERVER_CONFIG)
    # 获取基础工具列表
    llm_tools = [*(await mcp_client.get_tools()), stake_action, introduce_yourself, recommend_action, market_data]
    #llm_tools = [stake_action, introduce_yourself]
    
    # 检查是否启用RAG
    enable_rag = os.getenv('ENABLE_RAG', 'false').lower() == 'true'
    if enable_rag:
        llm_tools.append(retriever.retriever_tool)
        
    return llm_tools

call_model_name = "call_model"
@traceable
async def call_model(state: State, config: RunnableConfig, *, store: BaseStore) -> dict:
    """Extract the user's state from the conversation and update the memory."""
    configurable = configuration.Configuration.from_runnable_config(config)

#     # Retrieve the most recent memories for context
#     memories = await store.asearch(
#         ("memories", configurable.user_id),
#         query=str([m.content for m in state.messages[-3:]]),
#         limit=10,
#     )
#
#     # Format memories for inclusion in the prompt
#     formatted = "\n".join(f"[{mem.key}]: {mem.value} (similarity: {mem.score})" for mem in memories)
#     if formatted:
#         formatted = f"""
# <memories>
# {formatted}
# </memories>"""

    # Prepare the system prompt with user memories and current time
    # This helps the model understand the context and temporal relevance
    # sys = configurable.system_prompt.format(
    #     user_info=formatted
    # )
    # TODO 暂时不使用long term memory
    sys = configurable.system_prompt.format(
        user_info=''
    )

    # check context window size
    # Get context window size from environment variable (already loaded)
    llm_context_window = int(os.getenv("LLM_CONTEXT_WINDOW", 8000))
    filter_msgs = utils.filter_message(state.messages)

    messages_length = len(sys)
    for message in filter_msgs:
        if hasattr(message, 'content'):
            messages_length += len(message.content)

    # Check if total length exceeds context window
    # TODO 此处可以考虑使用消息截断的方式
    if messages_length > llm_context_window:
        return {"messages": [AIMessage(content="Sorry, the chat context is too large, please try a new chat thread")]}
    
    bind_tools = await get_tools()
    l = lambda:llm.bind_tools(bind_tools).invoke(
        [{"role": "system", "content": sys}, *filter_msgs],
        {"configurable": utils.split_model_and_provider(configurable.model)},
    )
    # the order of the tools may be important
    msg = await asyncio.to_thread(l)
    # print(sys)
    return {"messages": [msg]}


async def store_memory(state: State, config: RunnableConfig, *, store: BaseStore):
    # Extract tool calls from the last message
    msg = state.messages[-1]
    if not hasattr(msg, 'tool_calls') or not msg.tool_calls:
        # If there are no tool calls, return empty messages
        return {"messages": []}

    tool_calls = msg.tool_calls

    # Concurrently execute all upsert_memory calls
    saved_memories = await asyncio.gather(
        *(
            tools.upsert_memory(**tc["args"], config=config, store=store)
            for tc in tool_calls
        )
    )

    # Format the results of memory storage operations
    # This provides confirmation to the model that the actions it took were completed
    results = [
        {
            "role": "tool",
            "content": mem,
            "tool_call_id": tc["id"],
        }
        for tc, mem in zip(tool_calls, saved_memories)
    ]
    return {"messages": results}

toolNodeName = "tool_node_wrapper"

async def tool_node_wrapper(state: State, config: RunnableConfig, *, store: BaseStore) -> dict:
    bind_tools = await get_tools()
    return {"messages": await ToolNode(bind_tools).ainvoke(state.messages, config)}


def route_message(state: State):
    """Determine the next step based on the presence of tool calls."""
    msg = state.messages[-1]
    # Check if the message has tool_calls attribute and if it's not empty
    if hasattr(msg, 'tool_calls') and msg.tool_calls:
        if msg.tool_calls[-1]["name"] == "upsert_memory":
            # If there are tool calls, we need to store memories
            return "store_memory"
        else:
            return toolNodeName
    # Otherwise, finish; user can send the next message
    return END

def tool_router(state: State):
    """Determine the next step based on the presence of tool calls."""
    msg = state.messages[-1]
    if hasattr(msg, 'name') and msg.name and msg.name.endswith("_action"):
        return END
    return call_model_name



# Create the graph + all nodes
builder = StateGraph(State, config_schema=configuration.Configuration)

# Define the flow of the memory extraction process
builder.add_node(call_model)
builder.add_edge("__start__", call_model_name)
builder.add_conditional_edges(call_model_name, route_message, [toolNodeName, END])
# TODO 暂不使用 store_memory
# builder.add_node(store_memory)
# builder.add_conditional_edges(call_model_name, route_message, ["store_memory", toolNodeName, END])
# builder.add_edge("store_memory", call_model_name)
builder.add_node(tool_node_wrapper)
builder.add_conditional_edges(toolNodeName, tool_router, [call_model_name, END])
graph = builder.compile()
graph.name = "AlphaFiAgent"


__all__ = ["graph", "builder"]
