import os
import httpx
import logging
from typing import Dict, Any, Optional


class DifyService:
    def __init__(self):
        self.base_url = os.environ.get('DIFY_BASE_URL', 'http://10.1.177.121')
        self.api_version = 'v1'
        self.client = None
        self.logger = logging.getLogger(__name__)
        self.initialized = False

    def initialize(self):
        """初始化Dify服务"""
        # 设置超时参数为30秒
        self.client = httpx.Client(timeout=30.0)
        self.initialized = True
        self.logger.info('Dify service initialized with new client')
        return self

    def run_workflow(
        self,
        user_input: str,
        user: str = 'AlphaFi',
        response_mode: str = 'blocking',
        bear_token: str = None
    ) -> Dict[str, Any]:
        """
        调用Dify工作流API

        参数:
            user_input: 用户输入内容
            user: 用户标识，默认为'AlphaFi'
            response_mode: 响应模式，默认为'blocking'
            bear_token: Bearer令牌'

        返回:
            Dict[str, Any]: API响应结果
        """
        # 确保初始化
        if not self.initialized or self.client is None:
            self.initialize()

        # 构建请求头
        headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {bear_token}'
        }

        # 构建请求URL
        url = f'{self.base_url}/{self.api_version}/workflows/run'

        # 构建请求体
        payload = {
            'inputs': {
                'user_input': user_input
            },
            'response_mode': response_mode,
            'user': user
        }

        max_retries = 2
        retry_count = 0

        while retry_count <= max_retries:
            try:
                # 发送POST请求
                self.logger.info(f'Sending request to Dify workflow API: {url}')
                response = self.client.post(
                    url=url,
                    headers=headers,
                    json=payload
                )
                print(response.json())

                # 检查响应状态
                response.raise_for_status()
                return response.json()

            except httpx.TimeoutException as e:
                self.logger.error(f'Timeout error calling Dify workflow API: {e}')
                retry_count += 1
                if retry_count <= max_retries:
                    self.logger.info(f'Retrying ({retry_count}/{max_retries})...')
                    self.initialize()  # 重新初始化客户端
                else:
                    print(f'Error calling Dify workflow API after {max_retries} retries: {e}')
                    raise
            except httpx.HTTPError as e:
                self.logger.error(f'HTTP error calling Dify workflow API: {e}')
                # 如果是连接错误，尝试重新初始化客户端
                if isinstance(e, httpx.ConnectError):
                    retry_count += 1
                    if retry_count <= max_retries:
                        self.logger.info(f'Reconnecting and retrying ({retry_count}/{max_retries})...')
                        self.initialize()
                    else:
                        print(f'Error calling Dify workflow API after {max_retries} retries: {e}')
                        raise
                else:
                    print(f'Error calling Dify workflow API: {e}')
                    raise
            except Exception as e:
                print(f'Error calling Dify workflow API: {e}')
                raise

    def close(self):
        """关闭HTTP客户端连接"""
        if self.client:
            self.client.close()
            self.logger.info('Dify service client closed')
            self.initialized = False
        else:
            self.logger.info('Dify service client not initialized')


def initialize_dify_service() -> DifyService:
    """初始化Dify服务"""
    service = DifyService()
    service.initialize()
    return service