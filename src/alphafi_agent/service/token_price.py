import os
import asyncio
from datetime import datetime, timedelta, timezone
from typing import List, Dict, Any
from alphafi_agent.service.database import DatabaseService

class TokenPriceService:
    def __init__(self):
        self.db_service = None

    async def initialize(self):
        if self.db_service is None:
            self.db_service = await DatabaseService.get_instance()

    async def query_token_price(
        self,
        symbol: str = None,
        sort_column: str = "price_change_percentage_24h",
        sort_order: str = "desc",
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        # 确保服务已初始化
        await self.initialize()

        # 构建SQL查询
        query_parts = [
            "SELECT symbol, price_change_percentage_24h, name, current_price, ath, atl",
            "FROM coingecko_market_price",
            "WHERE price_change_percentage_24h IS NOT NULL"
        ]
        params = []

        # 如果symbol不为空，则添加到查询条件
        if symbol:
            query_parts.append("  AND LOWER(symbol) = %s")
            params.append(symbol.lower())

        # 添加排序和限制
        query_parts.append(f"ORDER BY {sort_column} {sort_order}")
        query_parts.append("LIMIT %s")
        params.append(limit)

        # 组合查询语句
        query = "\n".join(query_parts)

        # 执行查询
        params = tuple(params)
        result = await self.db_service.execute_query(query, params)


        return result


async def initialize_token_price_service():    
    service = TokenPriceService()
    await service.initialize()
    return service