from langgraph.checkpoint.base import BaseCheckpointSaver
from langgraph.checkpoint.redis import AsyncRedisSaver
from langgraph.checkpoint.memory import MemorySaver
import os
async def get_checkpointer() -> BaseCheckpointSaver:
    if 'redis' == os.getenv('CHECKPOINT_DB_TYPE', ''):
        # 此处使用ShallowRedisSaver，暂不需要time travel功能，节约空间
        # Configure TTL for checkpoint savers
        ttl_config = {
            "default_ttl": int(os.getenv("REDIS_TTL")),  # Default TTL in minutes
            "refresh_on_read": True,  # Refresh TTL when checkpoint is read
        } if os.getenv("REDIS_TTL") else None
        async with AsyncRedisSaver.from_conn_string(f"redis://{os.getenv('REDIS_HOST')}:{os.getenv('REDIS_PORT')}/{os.getenv('REDIS_DB')}", ttl=ttl_config) as checkpointer:
            await checkpointer.asetup()
            return checkpointer
    return MemorySaver()