from langgraph.checkpoint.base import BaseCheckpointSaver
from langgraph.constants import CONF, START
from langgraph.graph.state import CompiledStateGraph
import json

from alphafi_agent.chat_history import ChatHistory
from alphafi_agent.constants import MESSAGES, ChANNEL_VALUES, CONTENT, USER_ID, TS, CONFIG_KEY_THREAD_ID
from alphafi_agent.alphafi_graph import builder
from alphafi_agent.memory import get_checkpointer
from fastapi import FastAPI, Query, Form
from fastapi.responses import StreamingResponse
import uvicorn
from dotenv import load_dotenv
from pydantic import BaseModel
import os
import logging
from langgraph.types import Command
from fastapi.middleware.cors import CORSMiddleware
from langchain_core.messages import BaseMessage, message_to_dict

# 从service模块导入数据库服务、DeFi池服务和Token价格服务

logger = logging.getLogger(__name__)
from alphafi_agent.service.database import initialize_database
from alphafi_agent.service.defi_pool_service import DefiPoolService
from alphafi_agent.service.token_price import initialize_token_price_service

checkpointer: BaseCheckpointSaver
graph: CompiledStateGraph
chat_history = ChatHistory()
app = FastAPI()
class GenRequest(BaseModel):
    """GenRequest 请求模型

    Attributes:
        content (str): 用户输入内容，默认为空字符串。
        user_id (str): 用户 ID，默认为 "guest"。
        thread_id (str): 线程 ID，默认为 "thread-0"。
        stream (bool): 是否开启流式输出，默认为 False。
        resume (bool): 是否恢复执行模式，默认为 False。
    """
    content: str = ""
    user_id: str = "guest"
    thread_id: str = "thread-0"
    stream: bool = False
    resume: bool = False

@app.api_route("/alphafi/agent/gen", methods=["POST"], summary="生成内容接口", description="根据用户输入生成内容，支持流式和非流式输出; resume控制是否从上次中断处执行。 若消息类型为tool并且name带有_action后缀，则表示链上交互，content中的一项为raw_transaction,代表未签名的交易")
async def gen(request: GenRequest):
    """生成内容接口

    Args:
        request (GenRequest): 请求参数模型。

    Returns:
        Union[StreamingResponse, str]: 流式输出时返回 StreamingResponse，非流式输出时返回json字符串。
        若消息类型为tool并且name带有_action后缀，则表示链上交互，content中的一项为raw_transaction,代表未签名的交易.{"type":"tool","data":{"name": "stake_action", "content": "{\"raw_transaction\": \"to be signed\", \"amount\": 1.0}"}}
    """

    #收到请求的日志
    logger.info(f"Received request: {request.content}")
    if 'redis' == os.getenv('CHECKPOINT_DB_TYPE', ''):
        chat_history.store_message(request.user_id, request.thread_id, request.content)
    if request.stream:
        async def generate():
            input_param = Command(resume=request.content) if request.resume else {"messages": [{"role": "user", "content": request.content}]}
            async for chuck in graph.astream(
                input_param,
                {CONF:{USER_ID: request.user_id, CONFIG_KEY_THREAD_ID: request.thread_id}},
                stream_mode=["messages","updates"],
            ):
                # TODO: 当前仅支持messages模式返回json

                # 确保chuck是一个元组且至少有两个元素
                if isinstance(chuck, tuple) and len(chuck) >= 2:
                    # 处理messages类型
                    if chuck[0] == 'messages':
                        chunk_msg = json.dumps(message_to_dict(chuck[1][0]))
                        # print(f'===1==== {chunk_msg}')
                        yield chunk_msg
                    # 处理updates类型
                    elif chuck[0] == 'updates':
                        # 检查是否包含__interrupt__
                        if '__interrupt__' in chuck[1] and len(chuck[1]['__interrupt__']) > 0:
                            interrupt_value = chuck[1]['__interrupt__'][0].value
                            tool_response = {
                                "type": "interrupt",
                                "data": {
                                    "content": interrupt_value
                                }
                            }
                            yield json.dumps(tool_response)
                else:
                    # 其他情况，简单返回原始数据
                    yield chuck
        return StreamingResponse(generate())
    else:
        input_param = Command(resume=request.content) if request.resume else {"messages": [{"role":"user", "content": request.content}]}
        result = await graph.ainvoke(
            input_param,
            {CONF:{USER_ID: request.user_id, CONFIG_KEY_THREAD_ID: request.thread_id}},
        )
        response = {
            "messages": [
                message_to_dict(msg)
                if isinstance(msg, BaseMessage) else msg
                for msg in result["messages"]
            ]
        }
        if '__interrupt__' in result and len(result['__interrupt__']) > 0:
            response["messages"].append({"type": "interrupt", "data":{"content": result['__interrupt__'][0].value}})

        print(f'======= {json.dumps(response)}')
        return response


@app.get("/alphafi/agent/thread/list/{user_id}", summary="获取用户线程列表接口", description="根据用户 ID 获取包含 messages,thread_id和ts 的线程列表")
async def get_thread_list(user_id: str):
    """获取用户线程列表接口

    Args:
        user_id (str): 用户 ID

    Returns:
        list: 包含 messages, thread_id和 ts 的字典类型列表,且按照ts倒序排列
    """
    thread_list = []
    if 'redis' == os.getenv('CHECKPOINT_DB_TYPE', ''):
        thread_list = chat_history.get_user_messages(user_id)
    else:
        # redis不支持仅支持step 和 source作为filter
        # states = list(checkpointer.list(None, filter={USER_ID: user_id}))
        states = list(await checkpointer.alist(None))
        # 如果states不为空，则遍历states
        if states:
            # 初始化thread_list变量
            thread_list = []
            thread_id = states[0].config[CONF][CONFIG_KEY_THREAD_ID]
            ts = states[0].checkpoint[TS]
            thread_start_msg = ''
            for state in states:
                if thread_id != state.config[CONF][CONFIG_KEY_THREAD_ID]:
                    thread_list.append({MESSAGES: thread_start_msg, CONFIG_KEY_THREAD_ID: thread_id, TS: ts})
                    thread_start_msg = ''
                    thread_id = state.config[CONF][CONFIG_KEY_THREAD_ID]
                elif START in state.checkpoint[ChANNEL_VALUES]:
                    # 当前只获取用户的输入的检查点
                    thread_start_msg = state.checkpoint[ChANNEL_VALUES][START][MESSAGES][0][CONTENT]
                    ts = state.checkpoint[TS]
                    pass
                    # result = {MESSAGES:state.checkpoint[CHANNEL_VALUES][START][MESSAGES][0][CONTENT],CONFIG_KEY_CHECKPOINT_ID: state.checkpoint[ID],CONFIG_KEY_THREAD_ID:state.config[CONF][CONFIG_KEY_THREAD_ID]}
                    # print(result)
            thread_list.append({MESSAGES: thread_start_msg, CONFIG_KEY_THREAD_ID: thread_id, TS: ts})
    return sorted(thread_list, key=lambda x: x.get(TS, ''), reverse=True)

@app.get("/alphafi/agent/thread/msg/{thread_id}", summary="获取线程最新状态接口", description="根据线程 ID 和用户 ID 获取最最新状态")
async def get_thread_messages(thread_id: str, user_id: str):
    """获取线程最新状态接口

    Args:
        thread_id (str): 线程 ID
        user_id (str): 用户 ID

    Returns:
        list: 最新消息列表
    """
    state = await graph.aget_state({CONF:{CONFIG_KEY_THREAD_ID: thread_id}})
    if state and MESSAGES in state.values:
        return {
            MESSAGES: [
                message_to_dict(msg)
                if isinstance(msg, BaseMessage) else msg
                for msg in state.values[MESSAGES]
            ]
        }
    else:
        return {
            MESSAGES: []
        }


@app.get("/alphafi/agent/query/pool", summary="查询DeFi池信息接口", description="查询apy、tvlUsd、symbol、project信息")
async def query_defi_pools(
    sort_by: str = Query('apy', description="排序字段，可选值: apy, tvlUsd") ,
    sort_order: str = Query('desc', description="排序顺序，可选值: asc(升序), desc(降序)"),
    limit: int = Query(3, description="返回数据数量限制", ge=1),
    apy_low: float = Query(None, description="APY下限"),
    apy_high: float = Query(None, description="APY上限"),
    tvlUsd_low: float = Query(None, description="TVL USD下限"),
    tvlUsd_high: float = Query(None, description="TVL USD上限")
):
    """查询DeFi池信息接口

    Args:
        sort_by (str): 排序字段，默认为 'apy'
        sort_order (str): 排序顺序，默认为 'desc'
        limit (int): 返回数据数量限制，默认为 10
        apy_low (float, optional): APY下限
        apy_high (float, optional): APY上限
        tvlUsd_low (float, optional): TVL USD下限
        tvlUsd_high (float, optional): TVL USD上限

    Returns:
        list: 包含DeFi池信息的列表
    """
    # 验证排序字段
    valid_sort_fields = ['apy', 'tvlUsd']
    if sort_by not in valid_sort_fields:
        sort_by = 'apy'

    # 验证排序顺序
    valid_sort_orders = ['asc', 'desc']
    if sort_order.lower() not in valid_sort_orders:
        sort_order = 'desc'

    # 调用服务层获取数据
    result = await DefiPoolService.query_pools(
        sort_by=sort_by, 
        sort_order=sort_order, 
        limit=limit,
        apy_low=apy_low,
        apy_high=apy_high,
        tvlUsd_low=tvlUsd_low,
        tvlUsd_high=tvlUsd_high
    )

    return {
        "code": 200,
        "message": "success",
        "data": result
    }


@app.get("/alphafi/agent/query/token/price", summary="查询代币价格信息接口", description="查询price_change_percentage_24h、name、current_price、ath、atl、create_time信息，可选择按symbol过滤")
async def query_token_price(
    symbol: str = Query(None, description="代币符号，可选"),
    sort_column: str = Query("price_change_percentage_24h", description="排序字段，可选值: price_change_percentage_24h, current_price, ath, atl"),
    sort_order: str = Query("desc", description="排序顺序，可选值: asc(升序), desc(降序)"),
    limit: int = Query(5, description="返回数据数量限制", ge=1)
):
    """查询代币价格信息接口

    Args:
        symbol (str, optional): 代币符号，默认为None
        sort_column (str): 排序字段，默认为 'price_change_percentage_24h'
        sort_order (str): 排序顺序，默认为 'desc'
        limit (int): 返回数据数量限制，默认为 10

    Returns:
        dict: 包含代币价格信息的响应
    """
    # 验证排序字段
    valid_sort_fields = ['price_change_percentage_24h', 'current_price', 'ath', 'atl']
    if sort_column not in valid_sort_fields:
        sort_column = 'price_change_percentage_24h'

    # 验证排序顺序
    valid_sort_orders = ['asc', 'desc']
    if sort_order.lower() not in valid_sort_orders:
        sort_order = 'desc'

    # 调用服务层获取数据
    result = await token_price_service.query_token_price(
        symbol=symbol,
        sort_column=sort_column,
        sort_order=sort_order,
        limit=limit
    )

    return {
        "code": 200,
        "message": "success",
        "data": result
    }


async def __global_init__():
    load_dotenv()
    global checkpointer, graph
    checkpointer = await get_checkpointer()
    graph = builder.compile(checkpointer=checkpointer, name="AlphaFiAgent")
    # 初始化数据库连接
    await initialize_database()
    # 初始化Token价格服务
    global token_price_service
    token_price_service = await initialize_token_price_service()

if __name__ == "__main__":
    import asyncio
    asyncio.run(__global_init__())
    app.add_middleware(
        CORSMiddleware,
        allow_origins=['*'],
        allow_credentials=True,
        allow_methods=['*'],
        allow_headers=['*'],
    )
    # @app.middleware("http")
    # async def options_middleware(request, call_next):
    #     if request.method == "OPTIONS":
    #         return Response(headers={
    #             "Access-Control-Allow-Origin": "*",
    #             # "Access-Control-Allow-Methods": "*",
    #             # "Access-Control-Allow-Headers": "*",
    #             # "Access-Control-Allow-Credentials": "true"
    #         })
    #     return await call_next(request)
    port = int(os.getenv('PORT', 8000))
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=port,
        log_config={
            "version": 1,
            "formatters": {
                "default": {
                    "()": "uvicorn.logging.DefaultFormatter",
                    "fmt": "%(asctime)s - %(levelname)s - %(message)s",
                    "datefmt": "%Y-%m-%d %H:%M:%S"
                }
            },
            "handlers": {
                "default": {
                    "formatter": "default",
                    "class": "logging.StreamHandler",
                    "stream": "ext://sys.stderr"
                }
            },
            "loggers": {
                "uvicorn": {"handlers": ["default"], "level": "INFO"}
            }
        }
    )