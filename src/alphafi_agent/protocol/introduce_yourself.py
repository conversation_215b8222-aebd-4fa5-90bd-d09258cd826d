from langchain_core.tools import tool


@tool(return_direct = False, parse_docstring=True)
def introduce_yourself(amount: float) -> str:
    """
    introduce yourself or answer the question "Who you are".

    Args:
        None.

    Returns:
        str: introduction of yourself.
    """

    response = "I am <PERSON><PERSON><PERSON>, a financial assistant specializing in DeFi (decentralized finance). I can introduce DeFi related protocols, recommend DeFi yield strategies, assist you in performing on chain operations, and help you manage assets and transaction records."
    return response