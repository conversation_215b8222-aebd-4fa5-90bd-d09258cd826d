import json
import logging
from typing import Annotated

from langchain_core.runnables import RunnableConfig

# 初始化logger
logger = logging.getLogger(__name__)
from langchain_core.tools import tool
from langgraph.prebuilt import InjectedState
from langgraph.types import interrupt
from alphafi_agent.service.dify_service import initialize_dify_service
from alphafi_agent.utils import get_last_user_message
from langgraph.constants import CONF
from alphafi_agent.constants import USER_ID


# 初始化Dify服务
dify_service = initialize_dify_service()


@tool(return_direct=True, parse_docstring=True)
def recommend_action(messages: Annotated[list, InjectedState("messages")]) -> str:
    """
    recommend product based on apy or tvl

    Args:
        messages (list): Inject at runtime, does not need to assign value。

    Returns:
        str: recommended product list。

    """
    try:
        # 获取最后一个人工输入的消息
        last_user_message = get_last_user_message(messages)
        # 调用Dify服务获取推荐数据
        result = dify_service.run_workflow(user_input=last_user_message, bear_token='app-1LXhZIUqjFtx5HjM7oEXzhGf')
        
        # 解析返回的数据
        if result and 'data' in result and 'outputs' in result['data'] and 'data' in result['data']['outputs']:
            product_list = result['data']['outputs']['data']
            
            # 添加contract_address字段（如果需要）
            for product in product_list:
                if 'contract_address' not in product:
                    product['contract_address'] = '******************************************'
        else:
            # 如果数据格式不符合预期，使用默认空列表
            product_list = []
    except Exception as e:
        # 处理异常
        print(f"Error calling Dify service: {str(e)}")
        product_list = []
    
    return interrupt(json.dumps({"action_type":"recommend","products":product_list})) if len(product_list) > 0 else "no qualified product."

@tool(return_direct=True, parse_docstring=True)
def add_liquidity_action(config: RunnableConfig, project:str=None, project_fuzzy:bool = False, symbol:str=None, symbol_fuzzy:bool = False,apy_low: float = None, apy_high: float = None,
                         tvlUsd_low: float = None, tvlUsd_high: float = None) -> str:
    """
    添加流动性，包括质押池，借贷池，兑换池等收益池。入参包含了产品的过滤信息，可以根据这些信息进行DB筛选，更好的满足用户需求，其中项目名称和代币符号可模糊匹配。

    Args:
        project (str, optional): 项目或者协议名称。默认为None。
        symbol (str, optional): 代币符号。默认为None。
        symbol_fuzzy (bool, optional): 代币符号是否模糊匹配。默认为False。
        apy_low (float, optional): 年化收益率下限。默认为None。
        apy_high (float, optional): 年化收益率上限。默认为None。
        tvlUsd_low (float, optional): 流动性池tvl USD价值下限。默认为None。
        tvlUsd_high (float, optional): 流动性池tvl USD价值上限。默认为None。

    Returns:
        str: 返回一个JSON格式的字符串，包含推荐的操作类型,以及推荐的产品列表。

    """
    # 将所有入参构造成一个json对象
    params = {
        "project": project,
        "symbol": symbol,
        "symbol_fuzzy": symbol_fuzzy,
        "apy_low": apy_low,
        "apy_high": apy_high,
        "tvlUsd_low": tvlUsd_low,
        "tvlUsd_high": tvlUsd_high,
        "user_id": config[CONF][USER_ID],
    }
    
    # 过滤掉值为None的参数
    filtered_params = {k: v for k, v in params.items() if v is not None}
    logger.info(f"recommend params: {filtered_params}")


    # TODO 调用远程服务获取产品列表 此处mock
    product = {"apy": 10, "tvlUsd": 100000000.0, "contract_address": '******************************************',
               "project": 'lido' if 'project' not in filtered_params else filtered_params['project'],
               "symbol": 'ETH' if 'symbol' not in filtered_params else filtered_params['symbol'],
               "filtered_params": filtered_params}
    product_list = [product]
    
    return interrupt(json.dumps({"action_type": "recommend","products":product_list})) if len(product_list) > 0 else "no qualified product."


