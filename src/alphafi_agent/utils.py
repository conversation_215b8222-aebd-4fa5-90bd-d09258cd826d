"""Utility functions used in our graph."""
from langchain_core.messages import HumanMessage, AIMessage, ToolMessage


def split_model_and_provider(fully_specified_name: str) -> dict:
    """Initialize the configured chat model."""
    if "/" in fully_specified_name:
        provider, model = fully_specified_name.split("/", maxsplit=1)
    else:
        provider = None
        model = fully_specified_name
    return {"model": model, "provider": provider}


def get_last_user_message(messages: list) -> str:
    """Get the last user message from the messages list.

    Args:
        messages (list): List of messages from graph state.

    Returns:
        str: Content of the last user message, or empty string if no user message found.
    """
    last_user_message = None
    for message in reversed(messages):
        if isinstance(message, HumanMessage):
            last_user_message = message.content
            break

    return last_user_message if last_user_message is not None else ""


def filter_message(messages: list) -> list:
    """Filter AIMessages with tool calls that were not executed.

    Args:
        messages (list): List of messages from graph state.

    Returns:
        list: Filtered messages list.
    """
    tool_id_set = set()
    to_remove = set()

    # Single reverse pass to collect tool IDs and identify messages to remove
    for i in reversed(range(len(messages))):
        message = messages[i]
        if isinstance(message, ToolMessage):
            # Collect tool call IDs from ToolMessages
            tool_id_set.add(message.tool_call_id)
        elif isinstance(message, AIMessage) and message.tool_calls:
            # Check if all tool calls in this AIMessage have been executed
            all_executed = True
            for tool_call in message.tool_calls:
                if tool_call["id"] not in tool_id_set:
                    all_executed = False
                    break
            if not all_executed:
                # Mark this AIMessage for removal
                to_remove.add(i)
                # 消息列表中最多一条未被执行的工具指令，若发现，即可退出循环,可能有多个，全删除
                # break

    # Create filtered messages list by excluding marked indices
    filtered_messages = [msg for idx, msg in enumerate(messages) if idx not in to_remove]

    return filtered_messages
