PORT=8000
# To separate your traces from other application
LANGSMITH_PROJECT=alphafi-graph

# The following depend on your selected configuration

## LLM choice:
# ANTHROPIC_API_KEY=....
# FIREWORKS_API_KEY=...
OPENAI_API_KEY=********************************************************************************************************************************************************************

#llm settings
#MODEL_PROVIDER=ollama
#MODEL=llama3.2:3b
API_BASE=http://************:11434
#MODEL_PROVIDER=google_genai
#MODEL=gemini-2.0-flash

MODEL_PROVIDER=openai
MODEL=gpt-4.1-mini

#embedding settings
#EMBEDDING_PROVIDER=ollama
#EMBEDDING_MODEL=llama3.2:3b
#CHROMA_COLLECTION_NAME=documents_ollama_llama3.2-3b
EMBEDDING_API_BASE=http://************:11434
EMBEDDING_PROVIDER=gemini
EMBEDDING_MODEL=models/gemini-embedding-exp-03-07
CHROMA_COLLECTION_NAME=documents_gemini_models-gemini-embedding-exp-03-07
GOOGLE_API_KEY=AIzaSyA7-ue1fLWSi1W59QFiSqeU5GXQdFp964c

CHROMA_PERSIST_DIR=/root/chroma_space
CHROMA_HOST=************

LANGSMITH_API_KEY=***************************************************
LANGSMITH_TRACING=true
LANGSMITH_ENDPOINT=https://api.smith.langchain.com
LANGSMITH_PROJECT=alpha-fi-test

# for redis checkpoint
REDIS_URL=redis://*********:6379/0
REDIS_HOST=*********
REDIS_PORT=6379
# for redis checkpoint, 0 is necessary
REDIS_DB=0
REDIS_CHAT_HISTORY_DB=1
# ttl unit minutes: 10days optional
REDIS_TTL=14400
CHECKPOINT_DB_TYPE=redis
SEARCH_TYPE=similarity_score_threshold
SEARCH_TOP_K=3
SEARCH_SCORE_THRESHOLD=0.2
ENABLE_RAG=true
LLM_CONTEXT_WINDOW=800000
# MySQL数据库配置
MYSQL_HOST=*********
MYSQL_PORT=3306
MYSQL_USER=crawler_user
MYSQL_PASSWORD=jAhp6qFWXj
MYSQL_DATABASE=crawler_db